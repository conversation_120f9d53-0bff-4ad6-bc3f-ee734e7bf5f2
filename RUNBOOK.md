# Oracle Database Patching Automation - Operations Runbook

## Overview

This runbook provides step-by-step procedures for operating the Oracle Database Patching Automation framework in production environments.

## Pre-Patching Checklist

### 1. Environment Preparation

- [ ] Verify maintenance window schedule
- [ ] Confirm application downtime approval
- [ ] Check system resource availability
- [ ] Validate backup retention policies
- [ ] Ensure patch files are available
- [ ] Verify network connectivity to all RAC nodes (if applicable)

### 2. Configuration Verification

```bash
# Verify configuration
cat patch_config.conf | grep -E "ORACLE_HOME|ORACLE_SID|BACKUP_TYPE"

# Check Oracle environment
echo $ORACLE_HOME
echo $ORACLE_SID
sqlplus / as sysdba <<< "SELECT banner FROM v\$version;"
```

### 3. Pre-Patch Validation

```bash
# Run comprehensive validation
./oracle_patch_automation.sh validate

# Check specific components
./oracle_patch_automation.sh status
```

## Standard Patching Procedures

### Procedure 1: Standard Database Patch Application

**Objective**: Apply a standard Oracle database patch using OPatch

**Prerequisites**:
- Patch file downloaded and verified
- Maintenance window scheduled
- Backup completed within last 24 hours

**Steps**:

1. **Prepare Environment**
   ```bash
   # Set Oracle environment
   export ORACLE_SID=ORCL
   export ORACLE_HOME=/u01/app/oracle/product/19.0.0/dbhome_1
   
   # Verify database status
   sqlplus / as sysdba <<< "SELECT status FROM v\$instance;"
   ```

2. **Execute Patch Application**
   ```bash
   # Apply patch with full automation
   ./oracle_patch_automation.sh apply-patch \
     -p 12345678 \
     -s /patches/p12345678.zip \
     -t OPATCH \
     -c sha256_checksum_here
   ```

3. **Monitor Progress**
   ```bash
   # Monitor logs in real-time
   tail -f /u01/patches/logs/oracle_patch_*.log
   ```

4. **Verify Success**
   ```bash
   # Check patch application
   opatch lspatches
   
   # Verify database status
   ./oracle_patch_automation.sh health-check -p 12345678
   ```

**Expected Duration**: 30-60 minutes
**Rollback Time**: 15-30 minutes

### Procedure 2: RAC Environment Patching

**Objective**: Apply patches in Oracle RAC environment with rolling upgrade

**Prerequisites**:
- All RAC nodes accessible
- Cluster services running
- Load balancer configured for maintenance

**Steps**:

1. **Pre-RAC Validation**
   ```bash
   # Check cluster status
   crsctl stat res -t
   
   # Verify all nodes
   olsnodes -n
   ```

2. **Execute RAC Patching**
   ```bash
   # Apply patch to RAC cluster
   ./oracle_patch_automation.sh apply-patch \
     -p 12345678 \
     -s /patches/p12345678.zip \
     -t RAC
   ```

3. **Node-by-Node Verification**
   ```bash
   # Check each node
   for node in node1 node2 node3; do
     ssh $node "opatch lspatches | head -5"
   done
   ```

**Expected Duration**: 60-120 minutes
**Rollback Time**: 30-60 minutes

### Procedure 3: Emergency Rollback

**Objective**: Perform emergency rollback when patch application fails

**Trigger Conditions**:
- Database fails to start after patching
- Critical application functionality broken
- Data corruption detected
- Performance degradation beyond acceptable limits

**Steps**:

1. **Immediate Assessment**
   ```bash
   # Check database status
   sqlplus / as sysdba <<< "SELECT status FROM v\$instance;"
   
   # Review error logs
   tail -100 $ORACLE_HOME/diag/rdbms/$ORACLE_SID/$ORACLE_SID/trace/alert_$ORACLE_SID.log
   ```

2. **Execute Rollback**
   ```bash
   # Automatic rollback
   ./oracle_patch_automation.sh rollback -p PATCH_ID
   ```

3. **Manual Rollback (if automatic fails)**
   ```bash
   # Stop services
   lsnrctl stop
   sqlplus / as sysdba <<< "SHUTDOWN IMMEDIATE;"
   
   # Restore from backup (example for RMAN)
   rman target / <<EOF
   STARTUP NOMOUNT;
   RESTORE CONTROLFILE FROM AUTOBACKUP;
   ALTER DATABASE MOUNT;
   RESTORE DATABASE;
   RECOVER DATABASE;
   ALTER DATABASE OPEN RESETLOGS;
   EOF
   
   # Start services
   lsnrctl start
   ```

**Expected Duration**: 15-45 minutes
**Success Criteria**: Database operational with pre-patch functionality

## Monitoring and Alerting

### Key Metrics to Monitor

1. **System Resources**
   ```bash
   # CPU and Memory
   top -b -n1 | head -20
   
   # Disk Space
   df -h $ORACLE_HOME $PATCH_BASE_DIR
   
   # I/O Statistics
   iostat -x 1 5
   ```

2. **Database Health**
   ```bash
   # Database Status
   sqlplus / as sysdba <<< "SELECT status FROM v\$instance;"
   
   # Session Count
   sqlplus / as sysdba <<< "SELECT COUNT(*) FROM v\$session;"
   
   # Wait Events
   sqlplus / as sysdba <<< "SELECT event, total_waits FROM v\$system_event WHERE total_waits > 0 ORDER BY total_waits DESC FETCH FIRST 10 ROWS ONLY;"
   ```

3. **Patch Status**
   ```bash
   # Applied Patches
   opatch lspatches
   
   # SQL Patch Registry
   sqlplus / as sysdba <<< "SELECT patch_id, status, action_time FROM dba_registry_sqlpatch ORDER BY action_time DESC;"
   ```

### Alert Thresholds

- **CPU Usage**: > 80% for more than 5 minutes
- **Memory Usage**: > 85% for more than 5 minutes
- **Disk Space**: < 10GB available in Oracle Home
- **Database Response**: > 5 seconds for simple queries
- **Error Rate**: Any ORA-600 or ORA-7445 errors

## Troubleshooting Guide

### Common Issues and Resolutions

#### Issue 1: OPatch Prerequisites Failed

**Symptoms**:
- OPatch reports prerequisite failures
- Patch application aborts early

**Resolution**:
```bash
# Check OPatch version
opatch version

# Update OPatch if needed
cd $ORACLE_HOME
mv OPatch OPatch.old
unzip -q /patches/p6880880_*.zip

# Retry patch application
./oracle_patch_automation.sh apply-patch -p PATCH_ID -s PATCH_FILE -t OPATCH
```

#### Issue 2: Database Fails to Start After Patching

**Symptoms**:
- Database startup fails with errors
- Alert log shows patch-related errors

**Resolution**:
```bash
# Check alert log
tail -100 $ORACLE_HOME/diag/rdbms/$ORACLE_SID/$ORACLE_SID/trace/alert_$ORACLE_SID.log

# Try startup upgrade mode
sqlplus / as sysdba <<< "STARTUP UPGRADE;"

# Run datapatch
datapatch -verbose

# Normal startup
sqlplus / as sysdba <<< "SHUTDOWN IMMEDIATE; STARTUP;"
```

#### Issue 3: Invalid Objects After Patching

**Symptoms**:
- High count of invalid objects
- Application functionality issues

**Resolution**:
```bash
# Recompile invalid objects
sqlplus / as sysdba <<< "@$ORACLE_HOME/rdbms/admin/utlrp.sql"

# Check remaining invalid objects
sqlplus / as sysdba <<< "SELECT owner, object_name, object_type FROM dba_objects WHERE status = 'INVALID';"

# Manual compilation if needed
sqlplus / as sysdba <<< "ALTER PACKAGE package_name COMPILE;"
```

#### Issue 4: Rollback Fails

**Symptoms**:
- Automatic rollback reports failure
- Database remains in inconsistent state

**Resolution**:
```bash
# Manual RMAN restore
rman target / <<EOF
STARTUP NOMOUNT;
RESTORE CONTROLFILE FROM AUTOBACKUP;
ALTER DATABASE MOUNT;
RESTORE DATABASE;
RECOVER DATABASE;
ALTER DATABASE OPEN RESETLOGS;
EOF

# If RMAN fails, use filesystem restore
systemctl stop oracle
cd $ORACLE_HOME
rm -rf *
tar -xzf /u01/patches/backups/oracle_home_backup.tar.gz
systemctl start oracle
```

## Emergency Contacts

### Escalation Matrix

**Level 1 - Database Team**
- Primary DBA: [Contact Information]
- Secondary DBA: [Contact Information]
- On-call Phone: [Phone Number]

**Level 2 - System Administration**
- Unix Admin: [Contact Information]
- Storage Admin: [Contact Information]
- Network Admin: [Contact Information]

**Level 3 - Vendor Support**
- Oracle Support: [SR Process]
- Hardware Vendor: [Contact Information]
- Storage Vendor: [Contact Information]

### Communication Templates

#### Patch Start Notification
```
Subject: Oracle Patch Application Started - [ENVIRONMENT]

Patch application has started for Oracle database [SID] on [HOSTNAME].
Patch ID: [PATCH_ID]
Expected Duration: [DURATION]
Expected Completion: [TIME]

Status updates will be provided every 30 minutes.
```

#### Patch Completion Notification
```
Subject: Oracle Patch Application Completed - [ENVIRONMENT]

Patch application has completed successfully for Oracle database [SID].
Patch ID: [PATCH_ID]
Start Time: [START_TIME]
End Time: [END_TIME]
Duration: [DURATION]

Database is operational and all health checks passed.
```

#### Emergency Notification
```
Subject: URGENT - Oracle Patch Rollback Required - [ENVIRONMENT]

Emergency rollback is in progress for Oracle database [SID].
Issue: [DESCRIPTION]
Rollback Method: [METHOD]
ETA for Resolution: [TIME]

All hands on deck. Please standby for updates.
```

## Post-Patching Activities

### Immediate Tasks (0-2 hours)

1. **Verify Application Functionality**
   ```bash
   # Test critical application connections
   # Run smoke tests
   # Verify batch job schedules
   ```

2. **Monitor Performance**
   ```bash
   # Check AWR reports
   # Monitor wait events
   # Verify response times
   ```

3. **Update Documentation**
   - Record patch application details
   - Update system inventory
   - Document any issues encountered

### Follow-up Tasks (24-48 hours)

1. **Performance Analysis**
   - Compare pre/post patch performance
   - Analyze AWR reports
   - Review application logs

2. **Backup Verification**
   - Verify post-patch backups
   - Test restore procedures
   - Update backup schedules

3. **Cleanup Activities**
   ```bash
   # Clean up old logs
   ./oracle_patch_automation.sh cleanup
   
   # Remove temporary files
   # Archive patch files
   ```

## Maintenance and Updates

### Monthly Tasks

- Review and update patch automation scripts
- Test rollback procedures in development
- Update contact information and escalation matrix
- Review and tune alert thresholds

### Quarterly Tasks

- Conduct disaster recovery testing
- Review and update backup strategies
- Performance tune automation scripts
- Update documentation and runbooks

### Annual Tasks

- Complete security review of automation framework
- Evaluate new Oracle patching features
- Update training materials
- Review and update change management procedures

## Compliance and Auditing

### Audit Trail Requirements

- All patch activities logged with timestamps
- User authentication and authorization tracked
- Change approvals documented
- Rollback procedures tested and documented

### Compliance Checks

```bash
# Verify audit logging
ls -la $PATCH_LOGS_DIR/patch_audit.log

# Check file permissions
find $SCRIPT_DIR -name "*.sh" -exec ls -la {} \;

# Verify backup retention
find $PATCH_BACKUP_DIR -name "*.json" -mtime +30
```

This runbook should be reviewed and updated regularly to reflect changes in the environment and lessons learned from patch application experiences.
