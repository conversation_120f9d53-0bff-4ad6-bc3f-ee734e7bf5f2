#!/bin/bash
# Oracle Database Patching - Logging Utilities
# ============================================

# Source configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/patch_config.conf"

# Initialize logging
LOG_FILE="$PATCH_LOGS_DIR/oracle_patch_$(date +%Y%m%d_%H%M%S).log"
AUDIT_LOG="$PATCH_LOGS_DIR/patch_audit.log"

# Create log directories
mkdir -p "$PATCH_LOGS_DIR"

# Logging functions
log_message() {
    local level="$1"
    local message="$2"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    local hostname=$(hostname)
    
    # Format: [TIMESTAMP] [HOSTNAME] [LEVEL] MESSAGE
    echo "[$timestamp] [$hostname] [$level] $message" | tee -a "$LOG_FILE"
    
    # Also log to audit file for important events
    if [[ "$level" == "ERROR" || "$level" == "WARN" || "$level" == "AUDIT" ]]; then
        echo "[$timestamp] [$hostname] [$level] $message" >> "$AUDIT_LOG"
    fi
}

log_debug() {
    [[ "$LOG_LEVEL" == "DEBUG" ]] && log_message "DEBUG" "$1"
}

log_info() {
    log_message "INFO" "$1"
}

log_warn() {
    log_message "WARN" "$1"
}

log_error() {
    log_message "ERROR" "$1"
}

log_audit() {
    log_message "AUDIT" "$1"
}

# Function to log command execution
execute_and_log() {
    local command="$1"
    local description="$2"
    
    log_info "Executing: $description"
    log_debug "Command: $command"
    
    local start_time=$(date +%s)
    local output
    local exit_code
    
    # Execute command and capture output
    output=$(eval "$command" 2>&1)
    exit_code=$?
    
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    if [[ $exit_code -eq 0 ]]; then
        log_info "SUCCESS: $description (Duration: ${duration}s)"
        log_debug "Output: $output"
    else
        log_error "FAILED: $description (Duration: ${duration}s, Exit Code: $exit_code)"
        log_error "Error Output: $output"
    fi
    
    return $exit_code
}

# Function to create patch report
generate_patch_report() {
    local patch_id="$1"
    local status="$2"
    local start_time="$3"
    local end_time="$4"
    local error_details="$5"
    
    local report_file="$PATCH_LOGS_DIR/patch_report_${patch_id}_$(date +%Y%m%d).html"
    
    cat > "$report_file" << EOF
<!DOCTYPE html>
<html>
<head>
    <title>Oracle Patch Report - $patch_id</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background-color: #f0f0f0; padding: 10px; border-radius: 5px; }
        .success { color: green; }
        .failure { color: red; }
        .warning { color: orange; }
        table { border-collapse: collapse; width: 100%; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <div class="header">
        <h1>Oracle Database Patch Report</h1>
        <p><strong>Generated:</strong> $(date)</p>
        <p><strong>Hostname:</strong> $(hostname)</p>
        <p><strong>Oracle SID:</strong> $ORACLE_SID</p>
    </div>
    
    <h2>Patch Summary</h2>
    <table>
        <tr><th>Attribute</th><th>Value</th></tr>
        <tr><td>Patch ID</td><td>$patch_id</td></tr>
        <tr><td>Status</td><td class="$(echo $status | tr '[:upper:]' '[:lower:]')">$status</td></tr>
        <tr><td>Start Time</td><td>$start_time</td></tr>
        <tr><td>End Time</td><td>$end_time</td></tr>
        <tr><td>Duration</td><td>$(( $(date -d "$end_time" +%s) - $(date -d "$start_time" +%s) )) seconds</td></tr>
    </table>
    
    $(if [[ -n "$error_details" ]]; then
        echo "<h2>Error Details</h2>"
        echo "<pre>$error_details</pre>"
    fi)
    
    <h2>System Information</h2>
    <table>
        <tr><td>Oracle Version</td><td>$(sqlplus -s / as sysdba <<< "SELECT banner FROM v\$version WHERE banner LIKE 'Oracle%';" 2>/dev/null | grep Oracle || echo "N/A")</td></tr>
        <tr><td>Database Status</td><td>$(sqlplus -s / as sysdba <<< "SELECT status FROM v\$instance;" 2>/dev/null | tail -1 || echo "N/A")</td></tr>
        <tr><td>Disk Space</td><td>$(df -h $ORACLE_HOME | tail -1 | awk '{print $4 " available"}')</td></tr>
    </table>
    
    <h2>Log Files</h2>
    <ul>
        <li><a href="$LOG_FILE">Main Log File</a></li>
        <li><a href="$AUDIT_LOG">Audit Log</a></li>
    </ul>
</body>
</html>
EOF
    
    log_info "Patch report generated: $report_file"
    echo "$report_file"
}

# Function to send email notification
send_notification() {
    local subject="$1"
    local message="$2"
    local attachment="$3"
    
    if [[ "$EMAIL_ENABLED" == "true" && -n "$EMAIL_RECIPIENTS" ]]; then
        local email_body="$message\n\nHostname: $(hostname)\nTimestamp: $(date)\nOracle SID: $ORACLE_SID"
        
        if [[ -n "$attachment" && -f "$attachment" ]]; then
            echo -e "$email_body" | mail -s "$subject" -a "$attachment" "$EMAIL_RECIPIENTS"
        else
            echo -e "$email_body" | mail -s "$subject" "$EMAIL_RECIPIENTS"
        fi
        
        log_info "Email notification sent to: $EMAIL_RECIPIENTS"
    fi
}

# Function to cleanup old logs
cleanup_old_logs() {
    log_info "Cleaning up logs older than $LOG_RETENTION_DAYS days"
    find "$PATCH_LOGS_DIR" -name "*.log" -type f -mtime +$LOG_RETENTION_DAYS -delete
    find "$PATCH_LOGS_DIR" -name "*.html" -type f -mtime +$LOG_RETENTION_DAYS -delete
}

# Initialize logging session
log_info "=== Oracle Database Patching Session Started ==="
log_info "Configuration loaded from: $SCRIPT_DIR/patch_config.conf"
log_info "Log file: $LOG_FILE"
