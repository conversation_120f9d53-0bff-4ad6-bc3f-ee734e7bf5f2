#!/bin/bash
# Oracle Database Patching - Backup Automation
# ============================================

# Source configuration and logging
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/patch_config.conf"
source "$SCRIPT_DIR/logging_utils.sh"

# Backup metadata
BACKUP_START_TIME=""
BACKUP_END_TIME=""
BACKUP_ID=""
BACKUP_STATUS=""

# Function to generate unique backup ID
generate_backup_id() {
    BACKUP_ID="PREPATCH_$(date +%Y%m%d_%H%M%S)_${ORACLE_SID}"
    echo "$BACKUP_ID"
}

# Function to create RMAN backup
create_rman_backup() {
    log_info "Starting RMAN backup for pre-patch protection..."
    
    BACKUP_START_TIME=$(date '+%Y-%m-%d %H:%M:%S')
    local backup_tag="PREPATCH_$(date +%Y%m%d_%H%M%S)"
    
    # Create backup directory
    mkdir -p "$RMAN_BACKUP_LOCATION"
    
    # RMAN backup script
    local rman_script="$PATCH_LOGS_DIR/rman_backup_${BACKUP_ID}.rcv"
    
    cat > "$rman_script" << EOF
CONFIGURE RETENTION POLICY TO RECOVERY WINDOW OF 7 DAYS;
CONFIGURE BACKUP OPTIMIZATION ON;
CONFIGURE DEFAULT DEVICE TYPE TO DISK;
CONFIGURE CHANNEL DEVICE TYPE DISK FORMAT '$RMAN_BACKUP_LOCATION/%d_%T_%s_%p.bkp';

RUN {
    ALLOCATE CHANNEL c1 DEVICE TYPE DISK;
    ALLOCATE CHANNEL c2 DEVICE TYPE DISK;
    
    # Full database backup
    BACKUP AS COMPRESSED BACKUPSET 
    TAG '$backup_tag'
    DATABASE 
    PLUS ARCHIVELOG DELETE INPUT;
    
    # Control file and spfile backup
    BACKUP CURRENT CONTROLFILE TAG '${backup_tag}_CTL';
    BACKUP SPFILE TAG '${backup_tag}_SPFILE';
    
    # Crosscheck and delete obsolete backups
    CROSSCHECK BACKUP;
    DELETE NOPROMPT OBSOLETE;
    
    RELEASE CHANNEL c1;
    RELEASE CHANNEL c2;
}

# List backup summary
LIST BACKUP SUMMARY;
EXIT;
EOF

    log_info "Executing RMAN backup with tag: $backup_tag"
    
    if execute_and_log "rman target / cmdfile='$rman_script'" "RMAN Full Database Backup"; then
        BACKUP_END_TIME=$(date '+%Y-%m-%d %H:%M:%S')
        BACKUP_STATUS="SUCCESS"
        log_info "RMAN backup completed successfully"
        
        # Store backup metadata
        store_backup_metadata
        
        return 0
    else
        BACKUP_END_TIME=$(date '+%Y-%m-%d %H:%M:%S')
        BACKUP_STATUS="FAILED"
        log_error "RMAN backup failed"
        return 1
    fi
}

# Function to create snapshot backup
create_snapshot_backup() {
    log_info "Starting snapshot backup for pre-patch protection..."
    
    BACKUP_START_TIME=$(date '+%Y-%m-%d %H:%M:%S')
    
    # Create snapshot directory
    mkdir -p "$SNAPSHOT_MOUNT_POINT"
    
    # Get database file locations
    local datafiles=$(sqlplus -s / as sysdba << EOF
SET PAGESIZE 0
SET FEEDBACK OFF
SET HEADING OFF
SELECT file_name FROM dba_data_files
UNION
SELECT file_name FROM dba_temp_files
UNION
SELECT member FROM v\$logfile;
EXIT;
EOF
)
    
    # Create LVM snapshots (example for Linux LVM)
    local snapshot_name="oracle_prepatch_$(date +%Y%m%d_%H%M%S)"
    
    # This is a template - adjust based on your storage configuration
    if command -v lvcreate >/dev/null 2>&1; then
        log_info "Creating LVM snapshot: $snapshot_name"
        
        # Identify volume groups containing Oracle files
        local vg_list=$(echo "$datafiles" | xargs -I {} df {} | awk 'NR>1 {print $1}' | xargs -I {} lvdisplay {} 2>/dev/null | grep "VG Name" | awk '{print $3}' | sort -u)
        
        for vg in $vg_list; do
            if [[ -n "$vg" ]]; then
                log_info "Creating snapshot for volume group: $vg"
                if execute_and_log "lvcreate -L5G -s -n ${snapshot_name}_${vg} /dev/${vg}/oracle_data" "LVM Snapshot Creation"; then
                    log_info "Snapshot created: ${snapshot_name}_${vg}"
                else
                    log_error "Failed to create snapshot for VG: $vg"
                    BACKUP_STATUS="FAILED"
                    return 1
                fi
            fi
        done
        
        BACKUP_END_TIME=$(date '+%Y-%m-%d %H:%M:%S')
        BACKUP_STATUS="SUCCESS"
        store_backup_metadata
        return 0
    else
        log_error "LVM tools not available for snapshot creation"
        BACKUP_STATUS="FAILED"
        return 1
    fi
}

# Function to create filesystem backup
create_filesystem_backup() {
    log_info "Starting filesystem backup for pre-patch protection..."
    
    BACKUP_START_TIME=$(date '+%Y-%m-%d %H:%M:%S')
    
    local backup_dest="$PATCH_BACKUP_DIR/filesystem_backup_$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$backup_dest"
    
    # Backup Oracle Home (excluding certain directories)
    log_info "Backing up Oracle Home to: $backup_dest"
    
    if execute_and_log "tar -czf '$backup_dest/oracle_home.tar.gz' -C '$ORACLE_HOME' --exclude='*.log' --exclude='trace' --exclude='dump' ." "Oracle Home Backup"; then
        log_info "Oracle Home backup completed"
    else
        log_error "Oracle Home backup failed"
        BACKUP_STATUS="FAILED"
        return 1
    fi
    
    # Backup important configuration files
    local config_backup="$backup_dest/config_files"
    mkdir -p "$config_backup"
    
    # Copy important files
    cp "$ORACLE_HOME/network/admin/"* "$config_backup/" 2>/dev/null || true
    cp "/etc/oratab" "$config_backup/" 2>/dev/null || true
    cp "$ORACLE_HOME/dbs/init${ORACLE_SID}.ora" "$config_backup/" 2>/dev/null || true
    cp "$ORACLE_HOME/dbs/spfile${ORACLE_SID}.ora" "$config_backup/" 2>/dev/null || true
    
    BACKUP_END_TIME=$(date '+%Y-%m-%d %H:%M:%S')
    BACKUP_STATUS="SUCCESS"
    store_backup_metadata
    
    log_info "Filesystem backup completed successfully"
    return 0
}

# Function to store backup metadata
store_backup_metadata() {
    local metadata_file="$PATCH_BACKUP_DIR/backup_metadata_${BACKUP_ID}.json"
    
    cat > "$metadata_file" << EOF
{
    "backup_id": "$BACKUP_ID",
    "backup_type": "$BACKUP_TYPE",
    "oracle_sid": "$ORACLE_SID",
    "oracle_home": "$ORACLE_HOME",
    "start_time": "$BACKUP_START_TIME",
    "end_time": "$BACKUP_END_TIME",
    "status": "$BACKUP_STATUS",
    "hostname": "$(hostname)",
    "backup_location": "$RMAN_BACKUP_LOCATION",
    "created_by": "oracle_patch_automation",
    "retention_days": 30
}
EOF
    
    log_info "Backup metadata stored: $metadata_file"
}

# Function to verify backup integrity
verify_backup_integrity() {
    log_info "Verifying backup integrity..."
    
    case "$BACKUP_TYPE" in
        "RMAN")
            verify_rman_backup
            ;;
        "SNAPSHOT")
            verify_snapshot_backup
            ;;
        "FILESYSTEM")
            verify_filesystem_backup
            ;;
        *)
            log_warn "Backup verification not implemented for type: $BACKUP_TYPE"
            return 1
            ;;
    esac
}

# Function to verify RMAN backup
verify_rman_backup() {
    log_info "Verifying RMAN backup integrity..."
    
    local verify_script="$PATCH_LOGS_DIR/rman_verify_${BACKUP_ID}.rcv"
    
    cat > "$verify_script" << EOF
RESTORE DATABASE VALIDATE;
EXIT;
EOF
    
    if execute_and_log "rman target / cmdfile='$verify_script'" "RMAN Backup Verification"; then
        log_info "RMAN backup verification successful"
        return 0
    else
        log_error "RMAN backup verification failed"
        return 1
    fi
}

# Function to verify snapshot backup
verify_snapshot_backup() {
    log_info "Verifying snapshot backup..."
    
    # Check if snapshots exist and are accessible
    local snapshot_count=$(lvs | grep -c "prepatch" 2>/dev/null || echo "0")
    
    if [[ $snapshot_count -gt 0 ]]; then
        log_info "Found $snapshot_count snapshot volumes"
        return 0
    else
        log_error "No snapshot volumes found"
        return 1
    fi
}

# Function to verify filesystem backup
verify_filesystem_backup() {
    log_info "Verifying filesystem backup..."
    
    local backup_files=$(find "$PATCH_BACKUP_DIR" -name "oracle_home.tar.gz" -mtime -1 | wc -l)
    
    if [[ $backup_files -gt 0 ]]; then
        log_info "Filesystem backup files found and verified"
        return 0
    else
        log_error "Filesystem backup verification failed"
        return 1
    fi
}

# Main backup function
create_pre_patch_backup() {
    log_info "=== Starting Pre-Patch Backup Process ==="
    
    # Generate backup ID
    generate_backup_id
    log_info "Backup ID: $BACKUP_ID"
    
    # Create backup based on configured type
    case "$BACKUP_TYPE" in
        "RMAN")
            create_rman_backup
            ;;
        "SNAPSHOT")
            create_snapshot_backup
            ;;
        "FILESYSTEM")
            create_filesystem_backup
            ;;
        *)
            log_error "Unknown backup type: $BACKUP_TYPE"
            return 1
            ;;
    esac
    
    local backup_result=$?
    
    # Verify backup if creation was successful
    if [[ $backup_result -eq 0 ]]; then
        verify_backup_integrity
        local verify_result=$?
        
        if [[ $verify_result -eq 0 ]]; then
            log_info "=== Pre-Patch Backup Completed Successfully ==="
            log_audit "Pre-patch backup created and verified: $BACKUP_ID"
            return 0
        else
            log_error "=== Pre-Patch Backup Verification Failed ==="
            return 1
        fi
    else
        log_error "=== Pre-Patch Backup Creation Failed ==="
        return 1
    fi
}

# Export functions for use by main script
export -f create_pre_patch_backup
export -f generate_backup_id
