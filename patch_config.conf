# Oracle Database Patching Automation Configuration
# =================================================

# Oracle Environment Settings
export ORACLE_BASE="/u01/app/oracle"
export ORACLE_HOME="/u01/app/oracle/product/19.0.0/dbhome_1"
export ORACLE_SID="ORCL"
export PATH="$ORACLE_HOME/bin:$ORACLE_HOME/OPatch:$PATH"

# Database Connection Settings
DB_USER="sys"
DB_PASSWORD_FILE="/secure/oracle_sys_password"
DB_CONNECT_STRING="/ as sysdba"

# Patching Configuration
PATCH_BASE_DIR="/u01/patches"
PATCH_STAGING_DIR="$PATCH_BASE_DIR/staging"
PATCH_BACKUP_DIR="$PATCH_BASE_DIR/backups"
PATCH_LOGS_DIR="$PATCH_BASE_DIR/logs"

# Oracle Support Credentials (if using MOS integration)
MOS_USERNAME=""
MOS_PASSWORD_FILE="/secure/mos_credentials"

# Backup Configuration
BACKUP_TYPE="RMAN"  # Options: RMAN, SNAPSHOT, FILESYSTEM
RMAN_BACKUP_LOCATION="/u01/backups/rman"
SNAPSHOT_MOUNT_POINT="/u01/snapshots"

# Validation Thresholds
MIN_DISK_SPACE_GB=50
MAX_CPU_LOAD=80
MAX_MEMORY_USAGE=85

# RAC Configuration (if applicable)
RAC_ENABLED="false"
RAC_NODES=""  # Comma-separated list: "node1,node2,node3"

# Notification Settings
EMAIL_ENABLED="true"
EMAIL_RECIPIENTS="<EMAIL>,<EMAIL>"
SMTP_SERVER="smtp.company.com"

# Rollback Configuration
AUTO_ROLLBACK_ON_FAILURE="true"
ROLLBACK_TIMEOUT_MINUTES=30

# Logging Configuration
LOG_LEVEL="INFO"  # Options: DEBUG, INFO, WARN, ERROR
LOG_RETENTION_DAYS=30
CENTRAL_LOG_SERVER=""

# Scheduler Integration
SCHEDULER_TYPE="CRON"  # Options: CRON, JENKINS, ANSIBLE
MAINTENANCE_WINDOW_START="02:00"
MAINTENANCE_WINDOW_END="06:00"

# Version-Specific Settings
ORACLE_VERSION="19c"  # Options: 11g, 12c, 18c, 19c, 21c
OPATCH_VERSION_MIN="********.0"

# Security Settings
PATCH_VERIFICATION_ENABLED="true"
CHECKSUM_VERIFICATION="true"
