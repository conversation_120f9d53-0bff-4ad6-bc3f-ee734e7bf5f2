#!/bin/bash
# Oracle Database Patching - Rollback and Error Handling
# ======================================================

# Source configuration and logging
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/patch_config.conf"
source "$SCRIPT_DIR/logging_utils.sh"

# Rollback tracking variables
ROLLBACK_START_TIME=""
ROLLBACK_END_TIME=""
ROLLBACK_STATUS=""
ROLLBACK_METHOD=""

# Function to identify rollback method
identify_rollback_method() {
    local patch_id="$1"
    local rollback_info="$2"
    
    log_info "Identifying appropriate rollback method for patch: $patch_id"
    
    # Check if restore point exists
    if [[ "$rollback_info" == *"RESTORE_POINT"* ]]; then
        local restore_point=$(echo "$rollback_info" | cut -d':' -f2)
        log_info "Restore point available: $restore_point"
        ROLLBACK_METHOD="RESTORE_POINT"
        return 0
    fi
    
    # Check if RMAN backup is available
    local backup_metadata=$(find "$PATCH_BACKUP_DIR" -name "backup_metadata_*.json" -mtime -1 | head -1)
    if [[ -f "$backup_metadata" ]]; then
        log_info "Recent RMAN backup available: $backup_metadata"
        ROLLBACK_METHOD="RMAN_RESTORE"
        return 0
    fi
    
    # Check if OPatch rollback is possible
    local opatch_rollback=$(opatch lspatches | grep "$patch_id")
    if [[ -n "$opatch_rollback" ]]; then
        log_info "OPatch rollback possible for patch: $patch_id"
        ROLLBACK_METHOD="OPATCH_ROLLBACK"
        return 0
    fi
    
    # Check if filesystem backup is available
    local fs_backup=$(find "$PATCH_BACKUP_DIR" -name "oracle_home.tar.gz" -mtime -1 | head -1)
    if [[ -f "$fs_backup" ]]; then
        log_info "Filesystem backup available: $fs_backup"
        ROLLBACK_METHOD="FILESYSTEM_RESTORE"
        return 0
    fi
    
    log_error "No suitable rollback method identified"
    ROLLBACK_METHOD="MANUAL"
    return 1
}

# Function to rollback using restore point
rollback_with_restore_point() {
    local restore_point="$1"
    
    log_info "Performing rollback using restore point: $restore_point"
    
    # Check if flashback is enabled
    local flashback_status=$(sqlplus -s / as sysdba << EOF
SET PAGESIZE 0
SET FEEDBACK OFF
SET HEADING OFF
SELECT flashback_on FROM v\$database;
EXIT;
EOF
)
    
    flashback_status=$(echo "$flashback_status" | tr -d ' \n\r')
    
    if [[ "$flashback_status" != "YES" ]]; then
        log_error "Flashback database is not enabled - cannot use restore point"
        return 1
    fi
    
    # Shutdown database
    log_info "Shutting down database for flashback operation..."
    local shutdown_sql="SHUTDOWN IMMEDIATE;"
    
    if ! execute_and_log "sqlplus -s / as sysdba <<< \"$shutdown_sql\"" "Database Shutdown for Flashback"; then
        log_error "Failed to shutdown database for flashback"
        return 1
    fi
    
    # Start in mount mode
    log_info "Starting database in mount mode..."
    local mount_sql="STARTUP MOUNT;"
    
    if ! execute_and_log "sqlplus -s / as sysdba <<< \"$mount_sql\"" "Database Mount"; then
        log_error "Failed to mount database"
        return 1
    fi
    
    # Perform flashback
    log_info "Executing flashback database to restore point..."
    local flashback_sql="FLASHBACK DATABASE TO RESTORE POINT $restore_point;"
    
    if execute_and_log "sqlplus -s / as sysdba <<< \"$flashback_sql\"" "Flashback Database"; then
        log_info "Flashback database completed successfully"
        
        # Open database with resetlogs
        local open_sql="ALTER DATABASE OPEN RESETLOGS;"
        if execute_and_log "sqlplus -s / as sysdba <<< \"$open_sql\"" "Open Database Resetlogs"; then
            log_info "Database opened successfully after flashback"
            return 0
        else
            log_error "Failed to open database after flashback"
            return 1
        fi
    else
        log_error "Flashback database operation failed"
        return 1
    fi
}

# Function to rollback using RMAN restore
rollback_with_rman_restore() {
    local backup_metadata_file="$1"
    
    log_info "Performing rollback using RMAN restore from: $backup_metadata_file"
    
    # Parse backup metadata
    local backup_location=$(grep "backup_location" "$backup_metadata_file" | cut -d'"' -f4)
    local backup_id=$(grep "backup_id" "$backup_metadata_file" | cut -d'"' -f4)
    
    log_info "Backup location: $backup_location"
    log_info "Backup ID: $backup_id"
    
    # Shutdown database
    log_info "Shutting down database for RMAN restore..."
    local shutdown_sql="SHUTDOWN ABORT;"
    execute_and_log "sqlplus -s / as sysdba <<< \"$shutdown_sql\"" "Database Shutdown for Restore"
    
    # Start in nomount mode
    log_info "Starting database in nomount mode..."
    local nomount_sql="STARTUP NOMOUNT;"
    
    if ! execute_and_log "sqlplus -s / as sysdba <<< \"$nomount_sql\"" "Database Nomount"; then
        log_error "Failed to start database in nomount mode"
        return 1
    fi
    
    # Create RMAN restore script
    local restore_script="$PATCH_LOGS_DIR/rman_restore_${backup_id}.rcv"
    
    cat > "$restore_script" << EOF
CONFIGURE DEFAULT DEVICE TYPE TO DISK;
CONFIGURE CHANNEL DEVICE TYPE DISK FORMAT '$backup_location/%d_%T_%s_%p.bkp';

RUN {
    ALLOCATE CHANNEL c1 DEVICE TYPE DISK;
    ALLOCATE CHANNEL c2 DEVICE TYPE DISK;
    
    # Restore controlfile
    RESTORE CONTROLFILE FROM AUTOBACKUP;
    ALTER DATABASE MOUNT;
    
    # Restore database
    RESTORE DATABASE;
    
    # Recover database
    RECOVER DATABASE;
    
    # Open database
    ALTER DATABASE OPEN RESETLOGS;
    
    RELEASE CHANNEL c1;
    RELEASE CHANNEL c2;
}

EXIT;
EOF
    
    log_info "Executing RMAN restore operation..."
    
    if execute_and_log "rman target / cmdfile='$restore_script'" "RMAN Database Restore"; then
        log_info "RMAN restore completed successfully"
        return 0
    else
        log_error "RMAN restore operation failed"
        return 1
    fi
}

# Function to rollback using OPatch
rollback_with_opatch() {
    local patch_id="$1"
    
    log_info "Performing rollback using OPatch for patch: $patch_id"
    
    # Stop Oracle services
    source "$SCRIPT_DIR/patch_application.sh"
    
    if ! stop_oracle_services; then
        log_error "Failed to stop Oracle services for rollback"
        return 1
    fi
    
    # Perform OPatch rollback
    log_info "Executing OPatch rollback..."
    
    if execute_and_log "opatch rollback -id $patch_id -silent" "OPatch Rollback"; then
        log_info "OPatch rollback completed successfully"
        
        # Start Oracle services
        if start_oracle_services; then
            log_info "Oracle services restarted successfully after rollback"
            return 0
        else
            log_error "Failed to restart Oracle services after rollback"
            return 1
        fi
    else
        log_error "OPatch rollback operation failed"
        # Try to start services anyway
        start_oracle_services
        return 1
    fi
}

# Function to rollback using filesystem restore
rollback_with_filesystem_restore() {
    local backup_file="$1"
    
    log_info "Performing rollback using filesystem restore from: $backup_file"
    
    # Stop Oracle services
    source "$SCRIPT_DIR/patch_application.sh"
    
    if ! stop_oracle_services; then
        log_error "Failed to stop Oracle services for filesystem restore"
        return 1
    fi
    
    # Backup current Oracle Home (in case we need to revert)
    local current_backup="$PATCH_BACKUP_DIR/current_oracle_home_$(date +%Y%m%d_%H%M%S).tar.gz"
    log_info "Creating backup of current Oracle Home..."
    
    execute_and_log "tar -czf '$current_backup' -C '$ORACLE_HOME' ." "Backup Current Oracle Home"
    
    # Restore Oracle Home from backup
    log_info "Restoring Oracle Home from backup..."
    
    if execute_and_log "cd '$ORACLE_HOME' && tar -xzf '$backup_file'" "Restore Oracle Home"; then
        log_info "Oracle Home restored successfully"
        
        # Start Oracle services
        if start_oracle_services; then
            log_info "Oracle services restarted successfully after filesystem restore"
            return 0
        else
            log_error "Failed to restart Oracle services after filesystem restore"
            return 1
        fi
    else
        log_error "Filesystem restore operation failed"
        return 1
    fi
}

# Function to handle automatic rollback
handle_automatic_rollback() {
    local patch_id="$1"
    local rollback_info="$2"
    local error_details="$3"
    
    log_info "=== Starting Automatic Rollback Process ==="
    log_info "Patch ID: $patch_id"
    log_info "Error: $error_details"
    
    ROLLBACK_START_TIME=$(date '+%Y-%m-%d %H:%M:%S')
    
    # Identify rollback method
    if ! identify_rollback_method "$patch_id" "$rollback_info"; then
        log_error "Cannot identify suitable rollback method"
        ROLLBACK_STATUS="FAILED"
        return 1
    fi
    
    log_info "Selected rollback method: $ROLLBACK_METHOD"
    
    # Execute rollback based on method
    case "$ROLLBACK_METHOD" in
        "RESTORE_POINT")
            local restore_point=$(echo "$rollback_info" | cut -d':' -f2)
            rollback_with_restore_point "$restore_point"
            ;;
        "RMAN_RESTORE")
            local backup_metadata=$(find "$PATCH_BACKUP_DIR" -name "backup_metadata_*.json" -mtime -1 | head -1)
            rollback_with_rman_restore "$backup_metadata"
            ;;
        "OPATCH_ROLLBACK")
            rollback_with_opatch "$patch_id"
            ;;
        "FILESYSTEM_RESTORE")
            local fs_backup=$(find "$PATCH_BACKUP_DIR" -name "oracle_home.tar.gz" -mtime -1 | head -1)
            rollback_with_filesystem_restore "$fs_backup"
            ;;
        "MANUAL")
            log_error "Manual rollback required - automatic rollback not possible"
            ROLLBACK_STATUS="MANUAL_REQUIRED"
            return 1
            ;;
        *)
            log_error "Unknown rollback method: $ROLLBACK_METHOD"
            ROLLBACK_STATUS="FAILED"
            return 1
            ;;
    esac
    
    local rollback_result=$?
    ROLLBACK_END_TIME=$(date '+%Y-%m-%d %H:%M:%S')
    
    if [[ $rollback_result -eq 0 ]]; then
        ROLLBACK_STATUS="SUCCESS"
        log_info "=== Automatic Rollback Completed Successfully ==="
        log_audit "Automatic rollback successful for patch: $patch_id using method: $ROLLBACK_METHOD"
        
        # Send notification
        send_notification "Oracle Patch Rollback Successful" "Patch $patch_id has been rolled back successfully using $ROLLBACK_METHOD method."
        
        return 0
    else
        ROLLBACK_STATUS="FAILED"
        log_error "=== Automatic Rollback Failed ==="
        
        # Send notification
        send_notification "Oracle Patch Rollback Failed" "Automatic rollback failed for patch $patch_id. Manual intervention required."
        
        return 1
    fi
}

# Function to create rollback report
create_rollback_report() {
    local patch_id="$1"
    local error_details="$2"
    
    local report_file="$PATCH_LOGS_DIR/rollback_report_${patch_id}_$(date +%Y%m%d).txt"
    
    cat > "$report_file" << EOF
Oracle Patch Rollback Report
===========================

Patch ID: $patch_id
Rollback Method: $ROLLBACK_METHOD
Start Time: $ROLLBACK_START_TIME
End Time: $ROLLBACK_END_TIME
Status: $ROLLBACK_STATUS
Hostname: $(hostname)
Oracle SID: $ORACLE_SID

Original Error:
$error_details

Rollback Details:
- Method Used: $ROLLBACK_METHOD
- Duration: $(( $(date -d "$ROLLBACK_END_TIME" +%s) - $(date -d "$ROLLBACK_START_TIME" +%s) )) seconds

Post-Rollback Status:
- Database Status: $(sqlplus -s / as sysdba <<< "SELECT status FROM v\$instance;" 2>/dev/null | tail -1 || echo "N/A")
- Listener Status: $(lsnrctl status 2>/dev/null | grep -c "Service.*has.*instance" || echo "0") services

System Information:
- Current Time: $(date)
- Uptime: $(uptime)
- Disk Space: $(df -h $ORACLE_HOME | tail -1 | awk '{print $4 " available"}')

EOF
    
    log_info "Rollback report created: $report_file"
    echo "$report_file"
}

# Function to cleanup after rollback
cleanup_after_rollback() {
    local patch_id="$1"
    
    log_info "Performing cleanup after rollback..."
    
    # Remove staged patch files
    if [[ -n "$STAGED_PATCH_DIR" && -d "$STAGED_PATCH_DIR" ]]; then
        log_info "Removing staged patch directory: $STAGED_PATCH_DIR"
        rm -rf "$STAGED_PATCH_DIR"
    fi
    
    # Drop restore point if it exists and rollback was successful
    if [[ "$ROLLBACK_METHOD" == "RESTORE_POINT" && "$ROLLBACK_STATUS" == "SUCCESS" ]]; then
        local restore_point="PREPATCH_$(date +%Y%m%d)*"
        log_info "Cleaning up restore points..."
        
        sqlplus -s / as sysdba << EOF
DROP RESTORE POINT $restore_point;
EXIT;
EOF
    fi
    
    log_info "Rollback cleanup completed"
}

# Main rollback function
execute_rollback() {
    local patch_id="$1"
    local rollback_info="$2"
    local error_details="$3"
    
    # Check if automatic rollback is enabled
    if [[ "$AUTO_ROLLBACK_ON_FAILURE" != "true" ]]; then
        log_warn "Automatic rollback is disabled - manual intervention required"
        return 1
    fi
    
    # Execute automatic rollback
    if handle_automatic_rollback "$patch_id" "$rollback_info" "$error_details"; then
        # Create rollback report
        local report_file=$(create_rollback_report "$patch_id" "$error_details")
        
        # Cleanup
        cleanup_after_rollback "$patch_id"
        
        log_info "Rollback process completed successfully"
        return 0
    else
        # Create rollback report even if failed
        create_rollback_report "$patch_id" "$error_details"
        
        log_error "Rollback process failed - manual intervention required"
        return 1
    fi
}

# Export functions for use by main script
export -f execute_rollback
export -f identify_rollback_method
