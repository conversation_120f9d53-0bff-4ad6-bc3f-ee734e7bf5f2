#!/bin/bash
# Oracle Database Patching - Pre-Patch Validation
# ===============================================

# Source configuration and logging
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/patch_config.conf"
source "$SCRIPT_DIR/logging_utils.sh"

# Validation result tracking
VALIDATION_ERRORS=0
VALIDATION_WARNINGS=0

# Function to check disk space
check_disk_space() {
    log_info "Checking disk space requirements..."
    
    local oracle_home_space=$(df "$ORACLE_HOME" | tail -1 | awk '{print $4}')
    local oracle_home_space_gb=$((oracle_home_space / 1024 / 1024))
    
    local patch_dir_space=$(df "$PATCH_BASE_DIR" | tail -1 | awk '{print $4}')
    local patch_dir_space_gb=$((patch_dir_space / 1024 / 1024))
    
    log_info "Oracle Home available space: ${oracle_home_space_gb}GB"
    log_info "Patch directory available space: ${patch_dir_space_gb}GB"
    
    if [[ $oracle_home_space_gb -lt $MIN_DISK_SPACE_GB ]]; then
        log_error "Insufficient disk space in Oracle Home: ${oracle_home_space_gb}GB < ${MIN_DISK_SPACE_GB}GB"
        ((VALIDATION_ERRORS++))
        return 1
    fi
    
    if [[ $patch_dir_space_gb -lt $MIN_DISK_SPACE_GB ]]; then
        log_error "Insufficient disk space in patch directory: ${patch_dir_space_gb}GB < ${MIN_DISK_SPACE_GB}GB"
        ((VALIDATION_ERRORS++))
        return 1
    fi
    
    log_info "Disk space check: PASSED"
    return 0
}

# Function to check system load
check_system_load() {
    log_info "Checking system load and resource utilization..."
    
    local cpu_load=$(uptime | awk -F'load average:' '{print $2}' | awk '{print $1}' | sed 's/,//')
    local cpu_load_int=$(echo "$cpu_load" | cut -d'.' -f1)
    
    local memory_usage=$(free | grep Mem | awk '{printf "%.0f", $3/$2 * 100.0}')
    
    log_info "Current CPU load: $cpu_load"
    log_info "Memory usage: ${memory_usage}%"
    
    if [[ $cpu_load_int -gt $MAX_CPU_LOAD ]]; then
        log_warn "High CPU load detected: $cpu_load > $MAX_CPU_LOAD"
        ((VALIDATION_WARNINGS++))
    fi
    
    if [[ $memory_usage -gt $MAX_MEMORY_USAGE ]]; then
        log_warn "High memory usage detected: ${memory_usage}% > ${MAX_MEMORY_USAGE}%"
        ((VALIDATION_WARNINGS++))
    fi
    
    log_info "System load check: COMPLETED"
    return 0
}

# Function to check Oracle database status
check_database_status() {
    log_info "Checking Oracle database status..."
    
    # Check if Oracle processes are running
    local oracle_processes=$(ps -ef | grep -v grep | grep -c "$ORACLE_SID")
    log_info "Oracle processes running: $oracle_processes"
    
    # Check database status via SQL*Plus
    local db_status=$(sqlplus -s / as sysdba << EOF
SET PAGESIZE 0
SET FEEDBACK OFF
SET HEADING OFF
SELECT status FROM v\$instance;
EXIT;
EOF
)
    
    db_status=$(echo "$db_status" | tr -d ' \n\r')
    log_info "Database status: $db_status"
    
    if [[ "$db_status" != "OPEN" ]]; then
        log_error "Database is not in OPEN status: $db_status"
        ((VALIDATION_ERRORS++))
        return 1
    fi
    
    # Check listener status
    local listener_status=$(lsnrctl status 2>/dev/null | grep -c "Service.*has.*instance")
    if [[ $listener_status -eq 0 ]]; then
        log_warn "Listener may not be running or no services registered"
        ((VALIDATION_WARNINGS++))
    else
        log_info "Listener is running with $listener_status services"
    fi
    
    log_info "Database status check: PASSED"
    return 0
}

# Function to validate Oracle inventory
check_oracle_inventory() {
    log_info "Validating Oracle inventory and current patch level..."
    
    # Check OPatch version
    local opatch_version=$(opatch version 2>/dev/null | grep "OPatch Version" | awk '{print $3}')
    log_info "OPatch version: $opatch_version"
    
    if [[ -z "$opatch_version" ]]; then
        log_error "OPatch not found or not accessible"
        ((VALIDATION_ERRORS++))
        return 1
    fi
    
    # List currently applied patches
    log_info "Currently applied patches:"
    opatch lspatches 2>/dev/null | while read -r line; do
        log_info "  $line"
    done
    
    # Check Oracle inventory
    if [[ -f "$ORACLE_HOME/inventory/ContentsXML/oraclehomeproperties.xml" ]]; then
        log_info "Oracle inventory file exists"
    else
        log_warn "Oracle inventory file not found"
        ((VALIDATION_WARNINGS++))
    fi
    
    log_info "Oracle inventory check: COMPLETED"
    return 0
}

# Function to verify backup completion
check_backup_status() {
    log_info "Verifying recent backup completion..."
    
    case "$BACKUP_TYPE" in
        "RMAN")
            check_rman_backup
            ;;
        "SNAPSHOT")
            check_snapshot_backup
            ;;
        "FILESYSTEM")
            check_filesystem_backup
            ;;
        *)
            log_warn "Unknown backup type: $BACKUP_TYPE"
            ((VALIDATION_WARNINGS++))
            ;;
    esac
}

# Function to check RMAN backup
check_rman_backup() {
    log_info "Checking RMAN backup status..."
    
    local rman_check=$(rman target / << EOF
LIST BACKUP SUMMARY;
EXIT;
EOF
)
    
    # Check if backup completed within last 24 hours
    local recent_backup=$(sqlplus -s / as sysdba << EOF
SET PAGESIZE 0
SET FEEDBACK OFF
SET HEADING OFF
SELECT COUNT(*) FROM v\$backup_set 
WHERE completion_time > SYSDATE - 1;
EXIT;
EOF
)
    
    recent_backup=$(echo "$recent_backup" | tr -d ' \n\r')
    
    if [[ "$recent_backup" -gt 0 ]]; then
        log_info "Recent RMAN backup found: $recent_backup backup sets in last 24 hours"
    else
        log_error "No recent RMAN backup found within last 24 hours"
        ((VALIDATION_ERRORS++))
        return 1
    fi
    
    return 0
}

# Function to check snapshot backup
check_snapshot_backup() {
    log_info "Checking snapshot backup status..."
    
    if [[ -d "$SNAPSHOT_MOUNT_POINT" ]]; then
        local snapshot_count=$(ls -la "$SNAPSHOT_MOUNT_POINT" | wc -l)
        log_info "Snapshot directory exists with $snapshot_count entries"
    else
        log_error "Snapshot mount point not found: $SNAPSHOT_MOUNT_POINT"
        ((VALIDATION_ERRORS++))
        return 1
    fi
    
    return 0
}

# Function to check filesystem backup
check_filesystem_backup() {
    log_info "Checking filesystem backup status..."
    
    # This is a placeholder - implement based on your filesystem backup strategy
    log_warn "Filesystem backup check not implemented - manual verification required"
    ((VALIDATION_WARNINGS++))
    
    return 0
}

# Function to check Oracle version compatibility
check_version_compatibility() {
    log_info "Checking Oracle version compatibility..."
    
    local oracle_version=$(sqlplus -s / as sysdba << EOF
SET PAGESIZE 0
SET FEEDBACK OFF
SET HEADING OFF
SELECT version FROM v\$instance;
EXIT;
EOF
)
    
    oracle_version=$(echo "$oracle_version" | tr -d ' \n\r')
    log_info "Detected Oracle version: $oracle_version"
    
    # Version-specific checks
    case "$ORACLE_VERSION" in
        "11g")
            if [[ ! "$oracle_version" =~ ^11\. ]]; then
                log_warn "Configuration set for 11g but detected version: $oracle_version"
                ((VALIDATION_WARNINGS++))
            fi
            ;;
        "12c")
            if [[ ! "$oracle_version" =~ ^12\. ]]; then
                log_warn "Configuration set for 12c but detected version: $oracle_version"
                ((VALIDATION_WARNINGS++))
            fi
            ;;
        "19c")
            if [[ ! "$oracle_version" =~ ^19\. ]]; then
                log_warn "Configuration set for 19c but detected version: $oracle_version"
                ((VALIDATION_WARNINGS++))
            fi
            ;;
    esac
    
    log_info "Version compatibility check: COMPLETED"
    return 0
}

# Main validation function
run_pre_patch_validation() {
    log_info "=== Starting Pre-Patch Validation ==="
    
    # Reset counters
    VALIDATION_ERRORS=0
    VALIDATION_WARNINGS=0
    
    # Run all validation checks
    check_disk_space
    check_system_load
    check_database_status
    check_oracle_inventory
    check_backup_status
    check_version_compatibility
    
    # Summary
    log_info "=== Pre-Patch Validation Summary ==="
    log_info "Errors: $VALIDATION_ERRORS"
    log_info "Warnings: $VALIDATION_WARNINGS"
    
    if [[ $VALIDATION_ERRORS -gt 0 ]]; then
        log_error "Pre-patch validation FAILED with $VALIDATION_ERRORS errors"
        return 1
    elif [[ $VALIDATION_WARNINGS -gt 0 ]]; then
        log_warn "Pre-patch validation PASSED with $VALIDATION_WARNINGS warnings"
        return 0
    else
        log_info "Pre-patch validation PASSED successfully"
        return 0
    fi
}

# Export functions for use by main script
export -f run_pre_patch_validation
